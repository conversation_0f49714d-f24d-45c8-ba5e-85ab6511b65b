# Exemples d'utilisation - Perplexity MCP Server avec Augment

Ce document présente des exemples concrets d'utilisation du serveur MCP Perplexity avec Augment.

## Recherche Web (`search`)

### Recherche simple
```
Utilise perplexity-server pour rechercher les dernières nouvelles sur l'intelligence artificielle
```

### Recherche détaillée
```
Avec perplexity-server, fais une recherche détaillée sur les tendances du développement web en 2024
```

### Recherche technique
```
Recherche avec perplexity-server les meilleures pratiques pour l'optimisation des performances React
```

## Chat Conversationnel (`chat_perplexity`)

### Démarrer une conversation
```
Démarre une conversation avec perplexity-server sur l'informatique quantique
```

### Continuer une conversation
```
Continue notre discussion précédente avec perplexity-server sur les algorithmes quantiques
```

### Chat technique
```
Discute avec perplexity-server des avantages et inconvénients de TypeScript vs JavaScript
```

## Extraction de Contenu (`extract_url_content`)

### Extraction simple
```
Utilise perplexity-server pour extraire le contenu de https://docs.react.dev/learn
```

### Extraction avec exploration
```
Avec perplexity-server, extrait le contenu de https://nodejs.org/docs avec une profondeur de 2
```

### Extraction d'article
```
Extrait le contenu principal de cet article avec perplexity-server: https://example.com/article
```

## Documentation (`get_documentation`)

### Documentation d'API
```
Utilise perplexity-server pour obtenir la documentation de l'API REST de GitHub
```

### Documentation de librairie
```
Avec perplexity-server, trouve la documentation et des exemples pour la librairie Axios
```

### Documentation avec contexte
```
Demande à perplexity-server la documentation de Vue.js avec focus sur la composition API
```

## Recherche d'APIs (`find_apis`)

### APIs de paiement
```
Utilise perplexity-server pour trouver des APIs de paiement alternatives à Stripe
```

### APIs de géolocalisation
```
Avec perplexity-server, trouve des APIs de géolocalisation avec des tiers gratuits
```

### APIs avec contraintes
```
Recherche avec perplexity-server des APIs de traduction qui supportent Python et ont une version gratuite
```

## Analyse de Code (`check_deprecated_code`)

### Code React
```
Utilise perplexity-server pour analyser ce code React et identifier les parties dépréciées:
componentWillMount() {
  this.setState({loading: true});
}
```

### Code Python
```
Avec perplexity-server, vérifie si ce code Python utilise des fonctionnalités dépréciées:
import imp
imp.load_source('module', 'path/to/file.py')
```

### Code Node.js
```
Analyse ce code Node.js avec perplexity-server pour les dépréciations:
const fs = require('fs');
fs.exists('file.txt', (exists) => {
  console.log(exists);
});
```

## Cas d'usage avancés

### Recherche comparative
```
Compare avec perplexity-server les frameworks JavaScript modernes: React, Vue, Angular et Svelte
```

### Analyse de tendances
```
Utilise perplexity-server pour analyser les tendances actuelles en cybersécurité
```

### Veille technologique
```
Fais une veille technologique avec perplexity-server sur les nouveautés en machine learning
```

### Résolution de problèmes
```
Aide-moi avec perplexity-server à résoudre cette erreur: "Cannot read property 'map' of undefined"
```

### Apprentissage
```
Explique-moi avec perplexity-server les concepts de base de Docker avec des exemples pratiques
```

## Conseils d'utilisation

### Formulation des requêtes
- Soyez spécifique dans vos demandes
- Mentionnez explicitement "perplexity-server" pour déclencher l'outil
- Utilisez des mots-clés techniques appropriés

### Gestion des erreurs
- Si une requête échoue, reformulez différemment
- Vérifiez votre connexion internet
- Les timeouts sont normaux pour des requêtes complexes

### Optimisation
- Utilisez le niveau de détail approprié (brief/normal/detailed)
- Pour les conversations, utilisez le même chat_id
- Explorez les URLs avec la profondeur adaptée

### Limitations
- Respectez les limites de taux de Perplexity
- Évitez les requêtes trop fréquentes
- Certaines pages peuvent bloquer l'extraction automatique

## Dépannage

### Serveur ne répond pas
```
Vérifiez que le serveur fonctionne avec: node test-server.js
```

### Erreurs de navigation
```
Les erreurs de navigation vers Perplexity sont normales et gérées automatiquement
```

### Timeouts
```
Augmentez le timeout dans la configuration MCP si nécessaire
```

### Permissions
```
Vérifiez les permissions: chmod +x build/index.js
```
