# Perplexity MCP Server - Adaptation pour Augment

Ce projet est une adaptation du [perplexity-mcp-zerver](https://github.com/wysh3/perplexity-mcp-zerver) pour fonctionner avec Augment.

## Résumé des modifications

### Problèmes résolus
1. ✅ **Permissions d'exécution** : Ajout des permissions d'exécution au fichier `build/index.js`
2. ✅ **Dépendances natives** : Mi<PERSON> à jour de `better-sqlite3` vers la version `^11.10.0`
3. ✅ **Installation de Chrome** : Installation du navigateur Chrome pour Puppeteer
4. ✅ **Scripts npm** : Adaptation des scripts pour utiliser `npm` au lieu de `pnpm`

### Fonctionnalités
- **Recherche web** via Perplexity.ai sans clé API
- **Chat conversationnel** avec historique persistant
- **Extraction de contenu** depuis des URLs
- **Documentation** et recherche d'APIs
- **Analyse de code déprécié**

## Installation

### Prérequis
- Node.js (version 18 ou supérieure)
- npm
- Compilateur C++ (gcc) pour les modules natifs

### Étapes d'installation

1. **Cloner le projet** (déjà fait)
```bash
cd /home/<USER>/Desktop/perplexity-mcp-zerver
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Installer Chrome pour Puppeteer**
```bash
npx puppeteer browsers install chrome
```

4. **Compiler le projet TypeScript**
```bash
npm run build
```

5. **Tester le serveur**
```bash
timeout 10s node build/index.js
```

## Configuration pour Augment

### Fichier de configuration MCP

Créez ou modifiez votre fichier de configuration MCP (généralement `~/.config/augment/mcp_settings.json` ou similaire) :

```json
{
  "mcpServers": {
    "perplexity-server": {
      "command": "node",
      "args": [
        "/home/<USER>/Desktop/perplexity-mcp-zerver/build/index.js"
      ],
      "env": {},
      "disabled": false,
      "alwaysAllow": [],
      "autoApprove": [],
      "timeout": 300
    }
  }
}
```

**Important** : Remplacez le chemin `/home/<USER>/Desktop/perplexity-mcp-zerver/build/index.js` par le chemin absolu correct sur votre système.

## Outils disponibles

### 1. `search`
Effectue une recherche web via Perplexity.ai
- **Paramètres** : `query` (string), `detail_level` (brief/normal/detailed)
- **Usage** : Recherche d'informations générales

### 2. `chat_perplexity`
Chat conversationnel avec historique
- **Paramètres** : `message` (string), `chat_id` (optionnel)
- **Usage** : Conversations multi-tours avec contexte

### 3. `extract_url_content`
Extraction de contenu depuis des URLs
- **Paramètres** : `url` (string), `depth` (optionnel, 1-5)
- **Usage** : Extraction d'articles, documentation

### 4. `get_documentation`
Recherche de documentation technique
- **Paramètres** : `query` (string), `context` (optionnel)
- **Usage** : Documentation d'APIs, librairies

### 5. `find_apis`
Recherche d'APIs et services
- **Paramètres** : `requirement` (string), `context` (optionnel)
- **Usage** : Découverte d'APIs pour des besoins spécifiques

### 6. `check_deprecated_code`
Analyse de code déprécié
- **Paramètres** : `code` (string), `technology` (optionnel)
- **Usage** : Vérification de code legacy

## Utilisation avec Augment

Une fois configuré, vous pouvez utiliser les outils dans Augment :

```
Recherche les dernières nouvelles sur l'IA avec perplexity-server
```

```
Utilise perplexity-server pour extraire le contenu de https://example.com
```

```
Démarre une conversation avec perplexity-server sur l'informatique quantique
```

## Dépannage

### Problèmes courants

1. **Erreur de permissions**
```bash
chmod +x build/index.js
```

2. **Module better-sqlite3 non trouvé**
```bash
npm rebuild better-sqlite3
```

3. **Chrome non trouvé**
```bash
npx puppeteer browsers install chrome
```

4. **Erreur de compilation TypeScript**
```bash
npm run clean && npm run build
```

### Logs et débogage

Le serveur génère des logs détaillés. Pour déboguer :
- Vérifiez les permissions du fichier `build/index.js`
- Assurez-vous que Chrome est installé dans `~/.cache/puppeteer/`
- Vérifiez que le port n'est pas déjà utilisé

## Limitations

- **Dépendance web** : Nécessite une connexion internet
- **Stabilité** : Dépend de la structure du site Perplexity.ai
- **Performance** : L'automatisation web peut être lente
- **Détection** : Risque de détection comme bot par Perplexity

## Licence

GPL-3.0-or-later (comme le projet original)

## Crédits

- Projet original : [wysh3/perplexity-mcp-zerver](https://github.com/wysh3/perplexity-mcp-zerver)
- Adaptation pour Augment : Modifications pour compatibilité et stabilité
