# Reproduction de la Technique CLINE + Gemini Pro 2.5 pour Augment

## 🎯 Technique Originale Analysée

### Stack Technique de la Vidéo
- **CLINE** : Orchestrateur d'agent autonome
- **Gemini Pro 2.5** : LLM pour l'analyse et structuration
- **Brave Search MCP** : Recherche web gratuite
- **Fetch MCP** : Extraction de contenu web
- **Prompt adaptatif** : Structure CSV dynamique

### Workflow Original
1. **Brave Search** → Liste d'URLs pertinentes
2. **Fetch** → Contenu brut des pages
3. **Gemini Pro 2.5** → Analyse et structuration
4. **CSV dynamique** → Structure adaptée aux données

## 🔄 Adaptation pour Augment

### Stack Équivalente pour Augment
- **Augment** : Orchestrateur (remplace CLINE)
- **Claude/GPT** : LLM intégré (remplace Gemini Pro 2.5)
- **Perplexity MCP** : Recherche web (remplace Brave Search)
- **Extract URL Content** : Extraction web (remplace Fetch)
- **Prompt adaptatif** : Même principe de structure dynamique

## 📋 Prompt Exact Adapté pour Augment

### Prompt Principal (Inspiré de la Vidéo)
```
Tu vas créer un annuaire enrichi en utilisant la méthode de scraping adaptatif.

ÉTAPE 1 - RECHERCHE D'URLS
Utilise perplexity-server search pour trouver une liste d'URLs pertinentes sur [SUJET].
Recherche spécifiquement des sites officiels, annuaires, pages de listing.

ÉTAPE 2 - EXTRACTION DE CONTENU
Pour chaque URL trouvée, utilise perplexity-server extract_url_content pour récupérer le contenu complet.

ÉTAPE 3 - STRUCTURATION DYNAMIQUE
Analyse tout le contenu récupéré et crée un fichier CSV avec :
- AUTANT DE COLONNES QUE NÉCESSAIRE (pas de limite)
- TOUS LES DÉTAILS TROUVÉS (coordonnées, avis, tags, etc.)
- STRUCTURE ADAPTÉE aux données découvertes
- TAGS INTELLIGENTS générés à partir du contenu réel

RÈGLES IMPORTANTES :
✅ Ne te limite pas à des colonnes fixes
✅ Enrichis avec tous les tags, avis, et informations contextuelles
✅ Laisse les données guider la structure du CSV
✅ Ne remplis pas les champs vides - laisse vide si pas d'info
✅ Génère des tags basés sur le contenu réel (pas inventés)

Commence par analyser 5 entités pour définir la structure optimale.
```

### Prompt Spécialisé par Secteur

#### Restaurants
```
Utilise la méthode de scraping adaptatif pour créer un annuaire de restaurants à [VILLE].

1. Recherche avec perplexity-server search : "restaurants [VILLE] site:tripadvisor.com OR site:yelp.com OR site:google.com"
2. Extrait le contenu de chaque URL trouvée avec perplexity-server extract_url_content
3. Crée un CSV avec structure dynamique incluant :
   - Infos de base : nom, adresse, téléphone, site web
   - Détails culinaires : type cuisine, spécialités, prix moyen
   - Avis : extraits d'avis réels, notes, nombre d'avis
   - Services : terrasse, livraison, réservation, parking
   - Tags générés : "romantique", "familial", "végétarien", "terrasse"

Commence par 5 restaurants pour définir la structure.
```

#### Entreprises Tech
```
Utilise la méthode de scraping adaptatif pour créer un annuaire d'entreprises tech à [VILLE].

1. Recherche avec perplexity-server search : "startups tech [VILLE] site:linkedin.com OR site:crunchbase.com"
2. Extrait le contenu avec perplexity-server extract_url_content
3. Crée un CSV avec structure dynamique incluant :
   - Infos générales : nom, adresse, site web, LinkedIn
   - Business : secteur, produits/services, business model
   - Équipe : taille, fondateurs, employés clés
   - Financement : levées, investisseurs, valorisation
   - Tech : technologies utilisées, stack technique
   - Tags générés : "B2B", "SaaS", "AI", "fintech", "remote-friendly"

Commence par 5 entreprises pour définir la structure.
```

## 🛠️ MCPs Équivalents pour Augment

### Comparaison des MCPs

| Fonction | Vidéo Originale | Équivalent Augment | Performance |
|----------|-----------------|-------------------|-------------|
| **Recherche Web** | Brave Search MCP | Perplexity MCP | ✅ Supérieur |
| **Extraction Contenu** | Fetch MCP | Extract URL Content | ✅ Équivalent |
| **Orchestration** | CLINE | Augment | ✅ Intégré |
| **LLM** | Gemini Pro 2.5 | Claude/GPT | ✅ Supérieur |

### Avantages de l'Adaptation Augment
- ✅ **Perplexity MCP** : Plus puissant que Brave Search
- ✅ **Extract URL Content** : Gère JavaScript et contenu dynamique
- ✅ **Intégration native** : Pas besoin de configuration externe
- ✅ **LLM avancé** : Claude Sonnet pour l'analyse

## 🚀 Commandes Prêtes à Utiliser

### Commande Générique
```
Utilise la méthode de scraping adaptatif avec perplexity-server pour créer un annuaire de [TYPE] à [LIEU].

1. Recherche d'URLs pertinentes avec perplexity-server search
2. Extraction de contenu avec perplexity-server extract_url_content  
3. Création d'un CSV avec structure dynamique adaptée aux données trouvées
4. Génération de tags intelligents basés sur le contenu réel

Commence par 5 entités pour définir la structure optimale.
```

### Exemples Concrets

#### Boulangeries
```
Utilise la méthode de scraping adaptatif avec perplexity-server pour créer un annuaire de boulangeries artisanales à Lyon. Recherche des URLs sur TripAdvisor, Google, sites officiels. Extrait tout le contenu et crée un CSV avec structure dynamique. Commence par 5 boulangeries.
```

#### Coworking
```
Utilise la méthode de scraping adaptatif avec perplexity-server pour créer un annuaire d'espaces de coworking à Marseille. Recherche sur des sites spécialisés, LinkedIn, sites officiels. Crée un CSV avec structure adaptée aux données trouvées. Commence par 5 espaces.
```

#### Agences Immobilières
```
Utilise la méthode de scraping adaptatif avec perplexity-server pour créer un annuaire d'agences immobilières à Bordeaux. Recherche sur SeLoger, Logic-Immo, sites d'agences. Extrait toutes les infos et crée un CSV dynamique. Commence par 5 agences.
```

## 📊 Structure CSV Attendue

### Colonnes Dynamiques Typiques
```csv
nom,adresse,telephone,email,site_web,reseaux_sociaux,horaires,services,specialites,avis_extraits,note_moyenne,nombre_avis,prix_gamme,tags,coordonnees_gps,photos,certifications,equipe,histoire,particularites
```

### Tags Intelligents Générés
- **Restaurants** : "romantique,terrasse,végétarien,livraison,parking"
- **Tech** : "B2B,SaaS,AI,remote-friendly,startup,scale-up"
- **Services** : "premium,accessible,rapide,personnalisé,local"

## 🎯 Avantages de cette Méthode

### ✅ Supériorité sur l'Original
1. **Perplexity > Brave Search** : Recherche plus intelligente
2. **Extract URL > Fetch** : Gère le JavaScript et contenu dynamique
3. **Intégration native** : Pas de configuration MCP externe
4. **LLM avancé** : Claude pour une meilleure analyse

### ✅ Résultats Attendus
- **Structure adaptative** : CSV optimisé selon les données
- **Richesse maximale** : Toutes les infos disponibles collectées
- **Tags intelligents** : Générés à partir du contenu réel
- **Qualité garantie** : Validation croisée des sources

## 🔧 Optimisations Avancées

### Recherche Multi-Sources
```
Recherche sur plusieurs plateformes :
- Sites officiels
- Annuaires spécialisés  
- Réseaux sociaux
- Plateformes d'avis
- Presse locale
```

### Validation Croisée
```
Pour chaque information :
- Vérifier sur 2-3 sources minimum
- Prioriser les sources officielles
- Signaler les incohérences
```

### Enrichissement Progressif
```
1. Structure de base (5 entités)
2. Validation et ajustement
3. Extension à 20-50 entités
4. Optimisation finale
```

Cette adaptation reproduit fidèlement la technique de la vidéo tout en exploitant les avantages supérieurs d'Augment et du serveur MCP Perplexity !
