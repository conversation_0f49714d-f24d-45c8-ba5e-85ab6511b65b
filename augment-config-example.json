{"mcpServers": {"perplexity-server": {"command": "node", "args": ["/home/<USER>/Desktop/perplexity-mcp-zerver/build/index.js"], "env": {"NODE_ENV": "production"}, "disabled": false, "alwaysAllow": ["search", "extract_url_content"], "autoApprove": ["get_documentation"], "timeout": 300, "description": "Serveur MCP Perplexity pour recherche web sans API", "capabilities": ["web_search", "content_extraction", "documentation_lookup", "api_discovery", "code_analysis", "conversational_chat"]}}, "settings": {"defaultTimeout": 300, "maxRetries": 3, "logLevel": "info"}, "metadata": {"configVersion": "1.0.0", "lastUpdated": "2025-01-16", "description": "Configuration MCP pour Perplexity Server adapté à Augment"}}