#!/bin/bash

# Script d'installation automatique pour Perplexity MCP Server avec Augment
# Usage: ./install-for-augment.sh

set -e  # Arrêter en cas d'erreur

echo "🚀 Installation de Perplexity MCP Server pour Augment"
echo "=" | tr ' ' '=' | head -c 60 && echo

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérification des prérequis
log_info "Vérification des prérequis..."

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    log_error "Node.js n'est pas installé. Veuillez l'installer d'abord."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    log_error "Node.js version 18+ requis. Version actuelle: $(node --version)"
    exit 1
fi
log_success "Node.js $(node --version) détecté"

# Vérifier npm
if ! command -v npm &> /dev/null; then
    log_error "npm n'est pas installé."
    exit 1
fi
log_success "npm $(npm --version) détecté"

# Vérifier gcc pour les modules natifs
if ! command -v gcc &> /dev/null; then
    log_warning "gcc n'est pas installé. Installation recommandée pour les modules natifs."
    log_info "Sur Ubuntu/Debian: sudo apt install build-essential"
    log_info "Sur CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
fi

# Installation des dépendances
log_info "Installation des dépendances npm..."
if npm install; then
    log_success "Dépendances installées"
else
    log_error "Échec de l'installation des dépendances"
    exit 1
fi

# Installation de Chrome pour Puppeteer
log_info "Installation de Chrome pour Puppeteer..."
if npx puppeteer browsers install chrome; then
    log_success "Chrome installé pour Puppeteer"
else
    log_error "Échec de l'installation de Chrome"
    exit 1
fi

# Compilation TypeScript
log_info "Compilation du projet TypeScript..."
if npm run build; then
    log_success "Projet compilé"
else
    log_error "Échec de la compilation"
    exit 1
fi

# Ajout des permissions d'exécution
log_info "Configuration des permissions..."
if chmod +x build/index.js; then
    log_success "Permissions d'exécution ajoutées"
else
    log_error "Échec de l'ajout des permissions"
    exit 1
fi

# Test du serveur
log_info "Test du serveur..."
if node test-server.js; then
    log_success "Test du serveur réussi"
else
    log_warning "Test du serveur avec des avertissements (normal pour Perplexity)"
fi

# Affichage des informations de configuration
echo
echo "🎉 Installation terminée avec succès !"
echo
echo "📋 Configuration pour Augment:"
echo "   Chemin du serveur: $(pwd)/build/index.js"
echo "   Commande: node"
echo "   Arguments: [\"$(pwd)/build/index.js\"]"
echo
echo "📁 Fichiers de configuration créés:"
echo "   - augment-config-example.json (exemple de configuration)"
echo "   - README-AUGMENT.md (documentation)"
echo "   - test-server.js (script de test)"
echo
echo "🔧 Prochaines étapes:"
echo "   1. Copiez le chemin: $(pwd)/build/index.js"
echo "   2. Ajoutez-le à votre configuration MCP Augment"
echo "   3. Redémarrez Augment"
echo "   4. Testez avec: 'Recherche les dernières nouvelles sur l'IA'"
echo
echo "📖 Consultez README-AUGMENT.md pour plus de détails."
