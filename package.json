{"name": "perplexity-mcp-server", "version": "1.0.0", "description": "MCP server using Puppeteer to interact with Perplexity.ai without an API key - Adapted for Augment.", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js", "postinstall": "npm rebuild better-sqlite3 puppeteer", "clean": "rm -rf build/", "dev": "tsc --watch", "test": "node build/index.js --help"}, "keywords": ["mcp", "perplexity", "puppeteer", "ai", "research", "augment"], "author": "wysh3 (adapted for Augment)", "license": "GPL-3.0-or-later", "type": "module", "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "@mozilla/readability": "^0.6.0", "@types/axios": "^0.9.36", "@types/jsdom": "^21.1.7", "@types/mozilla__readability": "^0.4.2", "axios": "^1.8.4", "better-sqlite3": "^11.10.0", "jsdom": "^26.0.0", "puppeteer": "^24.2.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/node": "^22.13.1", "typescript": "^5.7.3"}}