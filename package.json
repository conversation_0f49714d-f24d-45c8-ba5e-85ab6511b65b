{"name": "perplexity-mcp-server", "version": "1.0.0", "description": "MCP server using Puppeteer to interact with Perplexity.ai without an API key.", "main": "build/index.js", "scripts": {"build": "tsc", "start": "node build/index.js", "postinstall": "pnpm rebuild better-sqlite3 puppeteer"}, "keywords": ["mcp", "perplexity", "puppeteer", "ai", "research"], "author": "wysh3", "license": "GPL-3.0-or-later", "type": "module", "dependencies": {"@modelcontextprotocol/sdk": "^1.4.1", "@mozilla/readability": "^0.6.0", "@types/axios": "^0.9.36", "@types/jsdom": "^21.1.7", "@types/mozilla__readability": "^0.4.2", "axios": "^1.8.4", "better-sqlite3": "^8.0.0", "jsdom": "^26.0.0", "puppeteer": "^24.2.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/node": "^22.13.1", "typescript": "^5.7.3"}}