# Prompt de Scraping Adaptatif pour Augment

## Prompt Principal (Inspiré de CLINE + Gemini Pro 2.5)

```
Tu vas créer un annuaire enrichi en utilisant une approche adaptative. Voici la méthode :

### PHASE 1 : RECHERCHE EXPLORATOIRE (5 entités)

1. **NE CRÉE PAS de structure CSV au départ** - laisse les données guider la structure
2. **Commence par 5 [ENTITÉS] seulement** pour ajuster la méthode
3. **Pour chaque entité, utilise TOUS les outils disponibles** :
   - `perplexity-server search` pour des recherches approfondies
   - `perplexity-server extract_url_content` pour scraper les sites officiels
   - `perplexity-server get_documentation` pour des infos techniques
   - `perplexity-server find_apis` si pertinent

### PHASE 2 : COLLECTE EXHAUSTIVE

Pour chaque entité, collecte :
- **Informations de base** : nom, adresse, téléphone, email, site web
- **Réseaux sociaux** : Facebook, Instagram, Twitter, LinkedIn
- **Avis et commentaires** : recherche des avis textuels réels
- **Images et logos** : URLs des images principales
- **Caractéristiques spéciales** : tout ce qui rend l'entité unique
- **Tags générés** : à partir des avis et descriptions (ex: "dog-friendly", "terrasse")
- **Coordonnées GPS** : si disponibles
- **Horaires d'ouverture** : si applicables

### PHASE 3 : ENRICHISSEMENT DYNAMIQUE

- **Si un site officiel ne fonctionne pas, passe au suivant**
- **Scrape plusieurs sources** pour chaque entité
- **Génère des tags intelligents** basés sur les avis et descriptions
- **Ne remplis pas les champs vides** - laisse vide si l'info n'existe pas
- **Adapte la structure** selon les données trouvées

### PHASE 4 : GÉNÉRATION FINALE

Après avoir collecté toutes les infos sur les 5 premières entités :
1. **Analyse les données collectées**
2. **Détermine les colonnes pertinentes** basées sur ce qui a été trouvé
3. **Génère un CSV structuré** avec toutes les colonnes nécessaires
4. **Valide la cohérence** des données

### RÈGLES IMPORTANTES

- ✅ Utilise `perplexity-server` pour toutes les recherches
- ✅ Collecte des avis textuels réels, pas de faux avis
- ✅ Génère des tags basés sur les données réelles
- ✅ Adapte la structure selon les données trouvées
- ❌ Ne force pas une structure prédéfinie
- ❌ Ne limite pas le nombre de colonnes
- ❌ Ne remplis pas avec des données inventées

### EXEMPLE D'UTILISATION

Remplace [ENTITÉS] par ton sujet :
- Restaurants à Paris
- Startups tech en France  
- Librairies indépendantes
- Centres de fitness
- Etc.
```

## Prompts Spécialisés par Domaine

### Pour les Restaurants
```
Crée un annuaire enrichi de restaurants en utilisant la méthode adaptative.

Commence par 5 restaurants pour définir la structure. Pour chaque restaurant :

1. Utilise `perplexity-server search` pour trouver des infos générales
2. Utilise `perplexity-server extract_url_content` sur leur site officiel
3. Recherche des avis sur Google, TripAdvisor, Yelp
4. Collecte : menu, prix, spécialités, ambiance, services
5. Génère des tags : "romantique", "familial", "terrasse", "végétarien"

Ne crée le CSV qu'après avoir analysé les 5 premiers restaurants.
```

### Pour les Entreprises Tech
```
Crée un annuaire enrichi d'entreprises tech en utilisant la méthode adaptative.

Commence par 5 entreprises pour définir la structure. Pour chaque entreprise :

1. Utilise `perplexity-server search` pour des infos générales
2. Utilise `perplexity-server extract_url_content` sur leur site et LinkedIn
3. Utilise `perplexity-server find_apis` pour leurs APIs publiques
4. Collecte : technologies utilisées, taille équipe, financement, produits
5. Génère des tags : "B2B", "SaaS", "AI", "blockchain", "remote-friendly"

Ne crée le CSV qu'après avoir analysé les 5 premières entreprises.
```

### Pour les Services Locaux
```
Crée un annuaire enrichi de [TYPE DE SERVICE] en utilisant la méthode adaptative.

Commence par 5 services pour définir la structure. Pour chaque service :

1. Utilise `perplexity-server search` pour des infos et avis
2. Utilise `perplexity-server extract_url_content` sur leur site
3. Recherche des avis clients réels
4. Collecte : services offerts, tarifs, zone de couverture, certifications
5. Génère des tags basés sur les spécialités et avis

Ne crée le CSV qu'après avoir analysé les 5 premiers services.
```

## Conseils d'Optimisation

### Utilisation des Outils MCP
- **`search`** : Pour les recherches générales et la découverte
- **`extract_url_content`** : Pour scraper les sites officiels
- **`chat_perplexity`** : Pour des conversations approfondies
- **`get_documentation`** : Pour des infos techniques
- **`find_apis`** : Pour découvrir des APIs liées

### Gestion des Erreurs
- Si un site ne répond pas, passe au suivant
- Si les données sont incomplètes, laisse les champs vides
- Si la structure évolue, adapte le CSV

### Validation des Données
- Vérifie la cohérence des coordonnées GPS
- Valide les URLs et emails
- Contrôle la pertinence des tags générés

## Exemple Complet d'Utilisation

```
Utilise la méthode de scraping adaptatif pour créer un annuaire de boulangeries artisanales à Lyon.

Commence par 5 boulangeries pour définir la structure optimale. 

Pour chaque boulangerie :
1. Recherche avec perplexity-server les infos générales
2. Extrait le contenu de leur site web
3. Collecte les avis clients réels
4. Identifie leurs spécialités (pain, pâtisserie, viennoiserie)
5. Génère des tags pertinents

Après analyse des 5 premières, crée un CSV avec la structure optimale découverte.
```
