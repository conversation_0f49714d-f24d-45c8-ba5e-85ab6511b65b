#!/usr/bin/env node

/**
 * Script de test pour le serveur MCP Perplexity
 * Vérifie que le serveur peut démarrer et répondre aux requêtes de base
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Test du serveur MCP Perplexity pour Augment');
console.log('=' .repeat(50));

// Test 1: Vérification des fichiers requis
console.log('\n1. Vérification des fichiers...');
import { existsSync } from 'fs';

const requiredFiles = [
    'build/index.js',
    'package.json',
    'tsconfig.json'
];

let allFilesExist = true;
for (const file of requiredFiles) {
    const exists = existsSync(join(__dirname, file));
    console.log(`   ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allFilesExist = false;
}

if (!allFilesExist) {
    console.log('\n❌ Fichiers manquants. Exécutez "npm run build" d\'abord.');
    process.exit(1);
}

// Test 2: Vérification des permissions
console.log('\n2. Vérification des permissions...');
import { accessSync, constants } from 'fs';

try {
    accessSync(join(__dirname, 'build/index.js'), constants.X_OK);
    console.log('   ✅ build/index.js est exécutable');
} catch (error) {
    console.log('   ❌ build/index.js n\'est pas exécutable');
    console.log('   💡 Exécutez: chmod +x build/index.js');
    process.exit(1);
}

// Test 3: Test de démarrage du serveur
console.log('\n3. Test de démarrage du serveur...');
console.log('   ⏳ Démarrage du serveur (timeout 15s)...');

const serverProcess = spawn('node', ['build/index.js'], {
    cwd: __dirname,
    stdio: ['pipe', 'pipe', 'pipe']
});

let serverOutput = '';
let serverError = '';

serverProcess.stdout.on('data', (data) => {
    serverOutput += data.toString();
});

serverProcess.stderr.on('data', (data) => {
    serverError += data.toString();
});

// Timeout après 15 secondes
const timeout = setTimeout(() => {
    serverProcess.kill('SIGTERM');
}, 15000);

serverProcess.on('close', (code) => {
    clearTimeout(timeout);
    
    console.log('\n4. Résultats du test...');
    
    // Analyser la sortie
    const hasNavigationAttempt = serverError.includes('Navigating to Perplexity.ai');
    const hasBrowserInit = serverError.includes('Browser initialization') || serverOutput.includes('Browser initialization');
    const hasServerRunning = serverError.includes('Perplexity MCP server running');
    
    console.log(`   ${hasNavigationAttempt ? '✅' : '❌'} Tentative de navigation vers Perplexity`);
    console.log(`   ${hasBrowserInit ? '✅' : '❌'} Initialisation du navigateur`);
    console.log(`   ${hasServerRunning ? '✅' : '❌'} Serveur MCP en cours d'exécution`);
    
    // Vérifier les erreurs critiques
    const hasCriticalError = serverError.includes('MODULE_NOT_FOUND') || 
                           serverError.includes('Cannot find module') ||
                           serverError.includes('Permission denied');
    
    if (hasCriticalError) {
        console.log('\n❌ Erreurs critiques détectées:');
        console.log(serverError);
        process.exit(1);
    }
    
    // Résumé
    console.log('\n5. Résumé...');
    if (hasNavigationAttempt && hasBrowserInit) {
        console.log('   ✅ Le serveur démarre correctement');
        console.log('   ✅ Puppeteer et Chrome fonctionnent');
        console.log('   ✅ Le serveur tente de se connecter à Perplexity');
        console.log('\n🎉 Test réussi ! Le serveur est prêt pour Augment.');
        
        console.log('\n📋 Configuration pour Augment:');
        console.log('   Chemin du serveur:', join(__dirname, 'build/index.js'));
        console.log('   Commande: node');
        console.log('   Arguments: ["' + join(__dirname, 'build/index.js') + '"]');
        
    } else {
        console.log('   ⚠️  Le serveur démarre mais avec des problèmes');
        console.log('   💡 Vérifiez la connexion internet et les dépendances');
        
        if (serverError) {
            console.log('\n📝 Erreurs détectées:');
            console.log(serverError.substring(0, 500) + (serverError.length > 500 ? '...' : ''));
        }
    }
    
    process.exit(0);
});

serverProcess.on('error', (error) => {
    clearTimeout(timeout);
    console.log('\n❌ Erreur lors du démarrage du serveur:');
    console.log(error.message);
    process.exit(1);
});

// Gestion de l'interruption
process.on('SIGINT', () => {
    console.log('\n\n⏹️  Test interrompu par l\'utilisateur');
    if (serverProcess && !serverProcess.killed) {
        serverProcess.kill('SIGTERM');
    }
    process.exit(0);
});
